<div class="form-card">
    <div class="row">
        <div class="col-12 mb-3">
            <h2 class="fs-title">Add your opening hours</h2>
            <p>Set standard opening hours to show on your profile page, these hours do not impact your calendar
                availability</p>
        </div>

        <div class="gray-card mb-10">
            <?php
                $daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
            ?>
            <?php $__currentLoopData = $daysOfWeek; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $day): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="time-picker-calendar2 mb-8">
                    <label class="days">
                        <input type="checkbox" value="<?php echo e($day); ?>" name="availability[<?php echo e($index); ?>][day]">
                        <span class="checkmark"><?php echo e($day); ?></span>
                    </label>
                    <div class="time-picker-range2">
                        <div class="checked-time">
                            <div class="start-time1">
                                <input type="text" name="availability[<?php echo e($index); ?>][start]"
                                    class="flatpickr-time mb-0 no_validate" placeholder="Select Time">
                            </div>
                            <p class="mb-0"> - </p>
                            <div class="end-time1">
                                <input type="text" name="availability[<?php echo e($index); ?>][end]"
                                    class="flatpickr-time mb-0 no_validate" placeholder="Select Time">
                            </div>
                        </div>

                        <div class="closed-time">
                            <p> Closed</p>
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>

        <div class="d-flex justify-content-between">
            <p>List of holidays</p>
            <label class="days holiday-list"> <input type="checkbox" class="select-all">
                <span class="checkmark">Select All</span>
            </label>
        </div>

        <div class="gray-card  mb-8">
            <?php $__currentLoopData = $holidays; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $holiday): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <div class="time-picker-calendar">
                    <div class="d-flex justify-content-between align-items-center">
                        <label class="days"><input type="checkbox" name="holidays[<?php echo e($index); ?>][holiday_id]"
                                value="<?php echo e($holiday->id); ?>" class="day-checkbox">
                            <span class="checkmark "><?php echo e($holiday->name ?? ''); ?></span>
                            <input type="hidden" name="holidays[<?php echo e($index); ?>][name]"
                                value="<?php echo e($holiday->name); ?>">
                            <input type="hidden" name="holidays[<?php echo e($index); ?>][date]"
                                value="<?php echo e($holiday->date); ?>">
                        </label>
                        <p><?php echo e($holiday->date ?? ''); ?></p>
                    </div>
                    <div class="start-time " style="display: none;">
                        <div class="d-flex gap-10 justify-content-center">
                            <input type="text" class="flatpickr-time no_validate" name="holidays[<?php echo e($index); ?>][start_time]"
                                placeholder="Select Time">
                            <p> - </p>
                            <input type="text" class="flatpickr-time no_validate" name="holidays[<?php echo e($index); ?>][end_time]"
                                placeholder="Select Time">
                        </div>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

            <button type="button" class="add-custom-holiday-btn">+ Add Custom Holiday</button>
            <!-- Modal -->
            <div id="customHolidayModal" class="HolidayModal modal" style="display:none;">
                <div class="modal-content">
                    <span class="close">&times;</span>
                    <h4 class="text-center mb-7">Add Custom Holiday</h4>
                    <label>Holiday Name:</label>
                    <input class="no_validate" type="text" id="customHolidayName" placeholder="Enter holiday name">
                    <label>Date:</label>
                    <input class="no_validate" type="text" id="customHolidayDate" placeholder="Select date">
                    <button type="button" class="blue-btn py-3" id="saveCustomHoliday">Save</button>
                </div>
            </div>
        </div>
        <p>Select holidays to show your availability. </p>
    </div>
</div>
<?php /**PATH D:\git-file\anders\resources\views/dashboard/templates/professional-acc-stepper/step4.blade.php ENDPATH**/ ?>