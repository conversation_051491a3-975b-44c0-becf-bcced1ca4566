<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CountriesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $countries = [
            ['name' => 'Afghanistan', 'country_code' => 'AF', 'flag' => 'https://flagcdn.com/w320/af.png'],
            ['name' => 'Albania', 'country_code' => 'AL', 'flag' => 'https://flagcdn.com/w320/al.png'],
            ['name' => 'Algeria', 'country_code' => 'DZ', 'flag' => 'https://flagcdn.com/w320/dz.png'],
            ['name' => 'Andorra', 'country_code' => 'AD', 'flag' => 'https://flagcdn.com/w320/ad.png'],
            ['name' => 'Angola', 'country_code' => 'AO', 'flag' => 'https://flagcdn.com/w320/ao.png'],
            ['name' => 'Argentina', 'country_code' => 'AR', 'flag' => 'https://flagcdn.com/w320/ar.png'],
            ['name' => 'Australia', 'country_code' => 'AU', 'flag' => 'https://flagcdn.com/w320/au.png'],
            ['name' => 'Austria', 'country_code' => 'AT', 'flag' => 'https://flagcdn.com/w320/at.png'],
            ['name' => 'Bangladesh', 'country_code' => 'BD', 'flag' => 'https://flagcdn.com/w320/bd.png'],
            ['name' => 'Belgium', 'country_code' => 'BE', 'flag' => 'https://flagcdn.com/w320/be.png'],
            ['name' => 'Brazil', 'country_code' => 'BR', 'flag' => 'https://flagcdn.com/w320/br.png'],
            ['name' => 'Canada', 'country_code' => 'CA', 'flag' => 'https://flagcdn.com/w320/ca.png'],
            ['name' => 'China', 'country_code' => 'CN', 'flag' => 'https://flagcdn.com/w320/cn.png'],
            ['name' => 'France', 'country_code' => 'FR', 'flag' => 'https://flagcdn.com/w320/fr.png'],
            ['name' => 'Germany', 'country_code' => 'DE', 'flag' => 'https://flagcdn.com/w320/de.png'],
            ['name' => 'India', 'country_code' => 'IN', 'flag' => 'https://flagcdn.com/w320/in.png'],
            ['name' => 'Indonesia', 'country_code' => 'ID', 'flag' => 'https://flagcdn.com/w320/id.png'],
            ['name' => 'Italy', 'country_code' => 'IT', 'flag' => 'https://flagcdn.com/w320/it.png'],
            ['name' => 'Japan', 'country_code' => 'JP', 'flag' => 'https://flagcdn.com/w320/jp.png'],
            ['name' => 'Kenya', 'country_code' => 'KE', 'flag' => 'https://flagcdn.com/w320/ke.png'],
            ['name' => 'Mexico', 'country_code' => 'MX', 'flag' => 'https://flagcdn.com/w320/mx.png'],
            ['name' => 'Nepal', 'country_code' => 'NP', 'flag' => 'https://flagcdn.com/w320/np.png'],
            ['name' => 'Netherlands', 'country_code' => 'NL', 'flag' => 'https://flagcdn.com/w320/nl.png'],
            ['name' => 'Nigeria', 'country_code' => 'NG', 'flag' => 'https://flagcdn.com/w320/ng.png'],
            ['name' => 'Pakistan', 'country_code' => 'PK', 'flag' => 'https://flagcdn.com/w320/pk.png'],
            ['name' => 'Russia', 'country_code' => 'RU', 'flag' => 'https://flagcdn.com/w320/ru.png'],
            ['name' => 'South Africa', 'country_code' => 'ZA', 'flag' => 'https://flagcdn.com/w320/za.png'],
            ['name' => 'United Arab Emirates', 'country_code' => 'AE', 'flag' => 'https://flagcdn.com/w320/ae.png'],
            ['name' => 'United Kingdom', 'country_code' => 'GB', 'flag' => 'https://flagcdn.com/w320/gb.png'],
            ['name' => 'United States', 'country_code' => 'US', 'flag' => 'https://flagcdn.com/w320/us.png'],
        ];

        DB::table('countries')->insert($countries);
    }
}
