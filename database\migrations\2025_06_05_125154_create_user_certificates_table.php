<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_certificates', function (Blueprint $table) {
            $table->id();
            $table->integer("user_id");
            $table->string("title")->nullable();
            $table->string("issued_by")->nullable();
            $table->date("issued_date")->nullable();
            $table->date("end_date")->nullable();
            $table->string("image")->nullable();
            $table->boolean("exception")->default(false);
            $table->string("exception_reason")->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_certificates');
    }
};
