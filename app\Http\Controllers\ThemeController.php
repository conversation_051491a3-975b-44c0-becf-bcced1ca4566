<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\View;

class ThemeController extends Controller
{
    function __construct()
    {

    }
    public function dashboard()
    {
        //dd(auth()->user()->getAllPermissions());
        return view('theme.index');
    }//end dashboard function.
    public function permissions()
    {
        return view('theme.user-management.permissions');
    }

    public function professional_account()
    {
        return view('auth.professional_account_stepper');
    }

    public function testing()
    {
        return view('dashboard.customer.testing');
    }
    public function familyFriends()
    {
        return view('dashboard.customer.family-friends');
    }
    public function addFriends()
    {
        return view('dashboard.customer.add-friend');
    }
    public function friendsDetails()
    {
        return view('dashboard.customer.friends-family-detail');
    }
    public function customerBooking()
    {
        return view('dashboard.customer.customer-booking');
    }
    public function customerWallet()
    {
        return view('dashboard.customer.customer-wallet');
    }
    public function favoriteProfessional()
    {
        return view('dashboard.customer.favorite_professional');
    }
    public function cart()
    {
        return view('dashboard.customer.cart');
    }

    public function professional_profile()
    {
        return view('dashboard.customer.professional_profile');
    }

    public function profileSetting()
    {
        return view('dashboard.profile_setting');
    }
    public function notification()
    {
        return view('dashboard.notification');
    }
    //    business controller
    public function businessAnalytics()
    {
        return view('dashboard.business.analytics');
    }
    public function businessBooking()
    {
        return view('dashboard.business.business-booking');
    }
    public function businessServices()
    {
        return view('dashboard.business.business-services');
    }
    public function addServices()
    {
        return view('dashboard.business.add-services');
    }
    public function addDiscount()
    {
        return view('dashboard.business.add_discount_coupon');
    }
    public function addStaffMember()
    {
        return view('dashboard.business.add-staff-member');
    }
    public function businessSetting()
    {
        return view('dashboard.business.setting');
    }
    public function businessEarning()
    {
        return view('dashboard.business.earning');
    }
    public function staffMember()
    {
        return view('dashboard.business.staff-member');
    }
    public function discountCoupon()
    {
        return view('dashboard.business.discount-coupons');
    }
    public function staffMemberDetails()
    {
        return view('dashboard.business.staff-member-details');
    }


    // Individual

    public function subscription()
    {
        return view('dashboard.individual.subscription');
    }


    // admin
    public function adminProfessionals()
    {
        return view('dashboard.admin.professionals');
    }
    public function adminCustomers()
    {
        return view('dashboard.admin.customers');
    }
    public function refundRequest()
    {
        return view('dashboard.admin.refund-request');
    }
    public function adminWallet()
    {
        return view('dashboard.admin.wallet');
    }
    public function adminVat()
    {
        return view('dashboard.admin.vat-mgmt');
    }
}
